package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CreatorInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 创作者信息 数据层
 */
public interface CreatorInfoMapper {
    /**
     * 查询创作者信息
     */
    public CreatorInfo selectCreatorInfoById(Long id);

    /**
     * 根据创作者ID查询创作者信息
     */
    public CreatorInfo selectCreatorInfoByCreatorId(String creatorId);

    /**
     * 根据手机号查询创作者信息
     */
    public CreatorInfo selectCreatorInfoByPhoneNumber(String phoneNumber);

    /**
     * 根据OpenID查询创作者信息
     */
    public CreatorInfo selectCreatorInfoByOpenid(String openid);

    /**
     * 根据OpenID和微信小程序APPID查询创作者信息
     */
    public CreatorInfo selectCreatorInfoByOpenidAndAppid(@Param("openid") String openid, @Param("wxMaAppid") String wxMaAppid);

    /**
     * 查询创作者信息列表
     */
    public List<CreatorInfo> selectCreatorInfoList(CreatorInfo creatorInfo);

    /**
     * 新增创作者信息
     */
    public int insertCreatorInfo(CreatorInfo creatorInfo);

    /**
     * 修改创作者信息
     */
    public int updateCreatorInfo(CreatorInfo creatorInfo);

    /**
     * 删除创作者信息
     */
    public int deleteCreatorInfoById(Long id);

    /**
     * 批量删除创作者信息
     */
    public int deleteCreatorInfoByIds(Long[] ids);

    /**
     * 校验创作者ID是否唯一
     */
    public CreatorInfo checkCreatorIdUnique(String creatorId);

    /**
     * 校验手机号是否唯一
     */
    public CreatorInfo checkPhoneNumberUnique(String phoneNumber);

    /**
     * 校验OpenID是否唯一
     */
    public CreatorInfo checkOpenidUnique(String openid);
}

package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.framework.web.jwt.CreatorJwtUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.CreatorInfo;
import com.ruoyi.system.service.ICreatorInfoService;

/**
 * 创作者信息管理
 */
@RestController
@RequestMapping("/creator_wx_login")
public class CreatorInfoController extends BaseController {
    @Autowired
    private ICreatorInfoService creatorInfoService;
    @Autowired
    private CreatorJwtUtil creatorJwtUtil;
    @Autowired
    private WxMaService wxMaService;

    /**
     * 微信登录
     * @param code wx.login接口返回的code
     * @return
     */
    @Anonymous
    @PostMapping(value = "/code2session")
    public AjaxResult code2session(@RequestParam("code") String code,@RequestParam("uuid") String uuid) {
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(code);
            String sessionKey = sessionInfo.getSessionKey();
            sessionInfo.setSessionKey(null);
            return success(sessionInfo);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 微信获取手机号
     * @param code getPhoneNumber方法返回的code
     * @return
     */
    @Anonymous
    @PostMapping(value = "/getUserPhoneNumber")
    public AjaxResult getUserPhoneNumber(@RequestParam("code") String code,@RequestParam("openid") String openid,@RequestParam("uuid") String uuid) {
        try {
            WxMaPhoneNumberInfo phoneNumber = wxMaService.getUserService().getPhoneNumber(code);
            return success(phoneNumber);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 小程序登录成功后更新创作者用户数据,openId,unionId,nickName,avatarUrl等等
     */
    @Anonymous
    @PostMapping(value = "/updateCreatorInfo")
    public AjaxResult updateCreatorInfo() {
        //TODO: 小程序登录成功后更新创作者用户数据,openId,unionId,nickName,avatarUrl等等
        return success();
    }

    /**
     * 获取创作者信息列表
     */
//    @GetMapping("/list")
//    public TableDataInfo list(CreatorInfo creatorInfo) {
//        startPage();
//        List<CreatorInfo> list = creatorInfoService.selectCreatorInfoList(creatorInfo);
//        return getDataTable(list);
//    }

//    /**
//     * 导出创作者信息列表
//     */
//    @Log(title = "创作者信息管理", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, CreatorInfo creatorInfo) {
//        List<CreatorInfo> list = creatorInfoService.selectCreatorInfoList(creatorInfo);
//        ExcelUtil<CreatorInfo> util = new ExcelUtil<CreatorInfo>(CreatorInfo.class);
//        util.exportExcel(response, list, "创作者信息数据");
//    }

    /**
     * 根据创作者信息编号获取详细信息
     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return success(creatorInfoService.selectCreatorInfoById(id));
//    }

    /**
     * 根据创作者ID获取详细信息
     */
    @Anonymous
    @GetMapping(value = "/queryCreatorInfo")
    public AjaxResult queryCreatorInfo() {
        String creatorId = creatorJwtUtil.getCreatorId();
        CreatorInfo creatorInfo = creatorInfoService.selectCreatorInfoByCreatorId(creatorId);
        if (creatorInfo == null) {
            return error("用户不存在");
        }
        creatorInfo.setPassword(null);
        return success(creatorInfo);
    }

//    /**
//     * 根据手机号获取详细信息
//     */
//    @GetMapping(value = "/phone/{phoneNumber}")
//    public AjaxResult getInfoByPhoneNumber(@PathVariable("phoneNumber") String phoneNumber) {
//        return success(creatorInfoService.selectCreatorInfoByPhoneNumber(phoneNumber));
//    }
//
//    /**
//     * 根据OpenID获取详细信息
//     */
//    @GetMapping(value = "/openid/{openid}")
//    public AjaxResult getInfoByOpenid(@PathVariable("openid") String openid) {
//        return success(creatorInfoService.selectCreatorInfoByOpenid(openid));
//    }
//
//    /**
//     * 新增创作者信息
//     */
//    @Log(title = "创作者信息管理", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@Validated @RequestBody CreatorInfo creatorInfo) {
//        // 校验创作者ID唯一性
//        if (!creatorInfoService.checkCreatorIdUnique(creatorInfo.getCreatorId())) {
//            return error("新增创作者'" + creatorInfo.getCreatorId() + "'失败，创作者ID已存在");
//        }
//        // 校验手机号唯一性
//        if (creatorInfo.getPhoneNumber() != null && !creatorInfoService.checkPhoneNumberUnique(creatorInfo.getPhoneNumber())) {
//            return error("新增创作者'" + creatorInfo.getCreatorId() + "'失败，手机号已存在");
//        }
//        // 校验OpenID唯一性
//        if (creatorInfo.getOpenid() != null && !creatorInfoService.checkOpenidUnique(creatorInfo.getOpenid())) {
//            return error("新增创作者'" + creatorInfo.getCreatorId() + "'失败，OpenID已存在");
//        }
//        return toAjax(creatorInfoService.insertCreatorInfo(creatorInfo));
//    }
//
//    /**
//     * 修改创作者信息
//     */
//    @Log(title = "创作者信息管理", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@Validated @RequestBody CreatorInfo creatorInfo) {
//        return toAjax(creatorInfoService.updateCreatorInfo(creatorInfo));
//    }
//
//    /**
//     * 删除创作者信息
//     */
//    @Log(title = "创作者信息管理", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids) {
//        return toAjax(creatorInfoService.deleteCreatorInfoByIds(ids));
//    }
//
//    /**
//     * 创作者登录
//     */
//    @PostMapping("/login")
//    public AjaxResult login(@RequestBody CreatorInfo loginInfo) {
//        CreatorInfo creatorInfo = creatorInfoService.login(loginInfo.getPhoneNumber(), loginInfo.getPassword());
//        if (creatorInfo != null) {
//            // 清除密码信息
//            creatorInfo.setPassword(null);
//            return success("登录成功", creatorInfo);
//        }
//        return error("手机号或密码错误");
//    }
//
//    /**
//     * 微信登录
//     */
//    @PostMapping("/wxLogin")
//    public AjaxResult wxLogin(@RequestBody CreatorInfo loginInfo) {
//        CreatorInfo creatorInfo = creatorInfoService.wxLogin(loginInfo.getOpenid(), loginInfo.getWxMaAppid());
//        if (creatorInfo != null) {
//            // 清除密码信息
//            creatorInfo.setPassword(null);
//            return success("微信登录成功", creatorInfo);
//        }
//        return error("微信登录失败，用户不存在");
//    }
//
//    /**
//     * 校验创作者ID
//     */
//    @GetMapping("/checkCreatorIdUnique")
//    public AjaxResult checkCreatorIdUnique(String creatorId) {
//        return success(creatorInfoService.checkCreatorIdUnique(creatorId));
//    }
//
//    /**
//     * 校验手机号
//     */
//    @GetMapping("/checkPhoneNumberUnique")
//    public AjaxResult checkPhoneNumberUnique(String phoneNumber) {
//        return success(creatorInfoService.checkPhoneNumberUnique(phoneNumber));
//    }
//
//    /**
//     * 校验OpenID
//     */
//    @GetMapping("/checkOpenidUnique")
//    public AjaxResult checkOpenidUnique(String openid) {
//        return success(creatorInfoService.checkOpenidUnique(openid));
//    }
}

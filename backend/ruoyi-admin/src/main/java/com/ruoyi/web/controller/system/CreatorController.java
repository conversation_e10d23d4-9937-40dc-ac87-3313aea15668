package com.ruoyi.web.controller.system;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.framework.web.jwt.CreatorJwtUtil;
import com.ruoyi.system.domain.CreatorInfo;
import com.ruoyi.system.domain.WxLogin;
import com.ruoyi.system.service.ICreatorInfoService;
import com.ruoyi.system.service.IFetchWxKtMiniAppTokenService;
import com.ruoyi.system.service.IWxLoginService;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/creator")
public class CreatorController extends BaseController {

    private static final String WECHAT_API_URL = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={token}";

    @Autowired
    private IFetchWxKtMiniAppTokenService fetchWxKtMiniAppTokenService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IWxLoginService wxLoginService;
    @Autowired
    private ICreatorInfoService creatorInfoService;
    @Autowired
    private CreatorJwtUtil creatorJwtUtil;

    @Autowired
    private WxMaService wxMaService;

//    @Autowired
//    private CreatorJwtUtil creatorJwtUtil;

    //private static final int DEFAULT_WIDTH = 430;

    /**
     * 1秒请求一次,前端轮询此接口
     * @param uuid
     * @return
     */
    @Anonymous
    @GetMapping("/login_status")
    public AjaxResult loginStatus(@RequestParam("uuid") String uuid) {
        //TODO:待完善
        WxLogin wxLogin = wxLoginService.checkLoginStatus(uuid);
        if (wxLogin != null) {
            //生成creatorToken,uuid去掉-
            //String creatorToken = UUID.randomUUID().toString();
            //creatorToken去除-
            //creatorToken = creatorToken.replace("-", "");
            //查询creatorInfo
            CreatorInfo creatorInfo = creatorInfoService.selectCreatorInfoByCreatorId(wxLogin.getCreatorId());
            if (creatorInfo == null) {
                return error("用户不存在");
            }
            //这个token是有过期时间的,当需要检验时先判断是否过期.
            String token = creatorJwtUtil.generateToken(wxLogin.getCreatorId());
            Map<String, Object> result = new HashMap<>();
            result.put("creatorToken", token);
            result.put("creatorInfo", creatorInfo);
            return success("登录成功", result);
        } else {
            return error("登录未完成或二维码已过期");
        }
    }
    /**
     * 创建小程序二维码并返回Base64编码
     */
    @Anonymous
    @GetMapping("/createQrcode")
    public AjaxResult createQrcode() {
        String scene = UUID.randomUUID().toString().replace("-", "");
        String page = "pages/pcWatchCourse/pcWatchCourse";
        WxMaCodeLineColor lineColor = new WxMaCodeLineColor();
        lineColor.setR("0");
        try {
            byte[] releases = wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(scene, page, true, "release", 430, false, lineColor, false);
            String base64String = Base64.getEncoder().encodeToString(releases);
            GetPcLoginResultDTO result = GetPcLoginResultDTO.builder()
                    .base64(base64String)
                    .uuid(scene)
                    .build();
            WxLogin wxLogin = new WxLogin();
            wxLogin.setUuid(scene);
            //当前时间加5分钟
            wxLogin.setExpirationTime(DateUtils.addMinutes(new Date(), 5));
            wxLoginService.insertWxLogin(wxLogin);
            return AjaxResult.success("二维码生成成功", result);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
//        String token;
//        try {
//            token = fetchWxKtMiniAppTokenService.fetchToken();
//        } catch (Exception e) {
//            log.error("获取access_token失败: {}", e.getMessage());
//            return error("服务不可用，请稍后重试");
//        }
//
//        // 1. 构建请求参数
//        Map<String, Object> requestParams = new HashMap<>();
//        //组装如下值
//        /*
//        {
//  "page": "pages/index/index",
//  "scene": "a=1",
//  "check_path": true,
//  "env_version": "release"
//}
//         */
//        //TODO:这个指向登录页面
//        requestParams.put("page", "pages/pcWatchCourse/pcWatchCourse");
//        /**
//         * 最大 32 个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
//         */
//        requestParams.put("scene", uuid);
//        requestParams.put("check_path", true);
//        requestParams.put("env_version", "release");
////        requestParams.put("path", path);
////        requestParams.put("width", DEFAULT_WIDTH);
//
//        // 2. 创建请求头
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//
//        // 3. 构建HTTP实体
//        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);
//
//        try {
//            // 4. 使用RestTemplate发送请求
//            ResponseEntity<byte[]> response = restTemplate.exchange(
//                    WECHAT_API_URL,
//                    HttpMethod.POST,
//                    requestEntity,
//                    byte[].class,
//                    token
//            );
//
//            // 5. 获取响应头
//            HttpHeaders responseHeaders = response.getHeaders();
//            MediaType contentType = responseHeaders.getContentType();
//
//            // 6. 根据Content-Type判断响应类型
//            if (isImageResponse(contentType)) {
//                return handleImageResponse(response.getBody(), uuid);
//            } else if (isJsonResponse(contentType)) {
//                return handleJsonErrorResponse(response.getBody());
//            } else {
//                return handleUnknownResponse(response);
//            }
//        } catch (Exception e) {
//            log.error("二维码生成失败: {}", e.getMessage(), e);
//            return error("二维码服务异常: " + e.getMessage());
//        }
    }

//    /**
//     * 检查响应是否是图片类型
//     * 根据实际响应头：Content-Type: image/jpeg
//     */
//    private boolean isImageResponse(MediaType mediaType) {
//        return mediaType != null &&
//                (mediaType.includes(MediaType.IMAGE_JPEG) ||
//                        mediaType.includes(MediaType.IMAGE_PNG));
//    }
//
//    /**
//     * 检查响应是否是JSON类型
//     * 根据实际错误响应头：Content-Type: application/json; encoding=utf-8
//     */
//    private boolean isJsonResponse(MediaType mediaType) {
//        return mediaType != null && mediaType.includes(MediaType.APPLICATION_JSON);
//    }
//
//    /**
//     * 处理图片响应
//     */
//    private AjaxResult handleImageResponse(byte[] imageData, String uuid) {
//        if (imageData == null || imageData.length == 0) {
//            return error("微信返回空的图片数据");
//        }
//
//        try {
//            String base64String = Base64.getEncoder().encodeToString(imageData);
//            GetPcLoginResultDTO result = GetPcLoginResultDTO.builder()
//                    .base64(base64String)
//                    .uuid(uuid)
//                    .build();
//            WxLogin wxLogin = new WxLogin();
//            wxLogin.setUuid(uuid);
//            //当前时间加5分钟
//            wxLogin.setExpirationTime(DateUtils.addMinutes(new Date(), 5));
//            wxLoginService.insertWxLogin(wxLogin);
//            return AjaxResult.success("二维码生成成功", result);
//        } catch (Exception e) {
//            log.error("Base64编码失败", e);
//            return error("二维码处理失败");
//        }
//    }
//
//    /**
//     * 处理JSON错误响应
//     */
//    private AjaxResult handleJsonErrorResponse(byte[] responseBytes) {
//        try {
//            String jsonResponse = new String(responseBytes, StandardCharsets.UTF_8);
//            JSONObject errorData = JSON.parseObject(jsonResponse);
//
//            int errorCode = errorData.getIntValue("errcode");
//            String errorMsg = errorData.getString("errmsg");
//
//            log.warn("微信API错误: code={}, message={}", errorCode, errorMsg);
//
//            // 根据您提供的错误示例40159进行特殊处理
//            if (errorCode == 40159) {
//                return error("二维码路径格式无效或数据格式错误");
//            }
//
//            // 其他常见错误处理
//            switch (errorCode) {
//                case 40001:
//                    return error("身份验证已过期，请稍后重试");
//                case 45009:
//                    return error("操作过于频繁，请稍后再试");
//                case 40014:
//                    return error("非法的访问令牌，请检查配置");
//                case 41030:
//                    return error("无效的页面路径");
//                default:
//                    return error(String.format("微信服务错误(%d): %s", errorCode, errorMsg));
//            }
//        } catch (Exception e) {
//            log.error("解析微信错误响应失败", e);
//            return error("未知的微信接口错误");
//        }
//    }
//
//    /**
//     * 处理未知类型的响应
//     */
//    private AjaxResult handleUnknownResponse(ResponseEntity<byte[]> response) {
//        HttpHeaders headers = response.getHeaders();
//        StringBuilder headerInfo = new StringBuilder("未知响应类型:");
//
//        headers.forEach((key, values) ->
//                headerInfo.append("\n").append(key).append(": ").append(String.join(", ", values)));
//
//        log.warn("无法识别的微信响应类型:\n{}", headerInfo);
//
//        return error("系统无法处理微信的响应格式");
//    }

    // DTO定义
    @Data
    @Builder
    public static class GetPcLoginResultDTO {
        private String uuid;
        private String base64;
    }
}